<?php

namespace App\Modules\RequestDelete\Services;

use App\Events\DomainHistoryEvent;
use App\Mail\UserDeleteRequestMail;
use App\Modules\Client\Constants\DomainStatus;
use App\Modules\Epp\Services\EppDomainService;
use App\Util\Constant\UserDomainStatus;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use App\Modules\CustomLogger\Services\AuthLogger;

class DomainDeleteService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        $DomainDeleteService = new self;

        return $DomainDeleteService;
    }

    public function approveDeleteRequestSave($request)
    {
        
        $data = $request->all();

        app(AuthLogger::class)->info($data);
        
        $eppInfo = EppDomainService::instance()->callEppDomainInfo($data['domainName']);
        $datastoreInfo = EppDomainService::instance()->callDatastoreDomainInfo($data['domainName']);

        // dd(isset($datastoreInfo['data'])?$datastoreInfo['data'] : $datastoreInfo);

        EppDomainService::instance()->callEppDomainDelete($data['domainName']);
        EppDomainService::instance()->callDatastoreDomainDelete($data['domainName']);

        self::localDelete($data);

        //send notification to user
        self::userNotification($data);

        //send email to user
        self::userEmailNotification($data);

        //domainHistory
        self::domainHistory($data);

        //Pending delete
        DB::client()->table('pending_domain_deletions')->insert([
            'registered_domain_id' => $data['domainId'],
            'deleted_by' => Auth::user()->email,
            'deleted_at' => now(),
        ]);
    }
    public function rejectDeleteRequestSave($request)
    {

        self::updateDomainDeletionRequestTable($request, 0);

        //update domain table
        DB::client()->table('domains')->where('id', $request['domainId'])->update([
            'status' => DomainStatus::ACTIVE,
            'deleted_at' => null,
            'updated_at' => now(),
        ]);
    }
    public function createDeleteRequestSave($request)
    {
        $data = $request->all();

        self::newDomainDelete($data);
        self::domainHistory($data);
        self::localDelete($data);

        //send notification to user's dashboard
        self::userNotification($data);

        //send email to user
        self::userEmailNotification($data);

        return;
    }
    private function localDelete($requestData)
    {
        // Update domain deletion request entry
        self::updateDomainDeletionRequestTable($requestData);

        $timestamp = now();

        $updates = [
            'status'     => UserDomainStatus::DELETED,
            'deleted_at' => $timestamp,
            'updated_at' => $timestamp,
        ];

        // Update registered_domains table
        DB::client()->table('registered_domains')
            ->where('domain_id', $requestData['domainId'])
            ->update($updates);

        // Update domains table
        DB::client()->table('domains')
            ->where('id', $requestData['domainId'])
            ->update($updates);
    }

    private function updateDomainDeletionRequestTable($requestData, $authID = null)
    {

        // if (!empty($requestData['domainId'] ?? null)) {
        //     return;
        // }

        $agentID = $authID ?? Auth::id();

        $date = Carbon::parse($requestData['createdDate']);

        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        return DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $requestData['domainId'])
            ->update([
                'support_agent_id' => $agentID,
                'support_agent_name' =>  Auth::user()->name . ' (' . Auth::user()->email . ')',
                'deleted_at' => now(),
                'feedback_date' => now(),
                'support_note' => $requestData['support_note'],
                'is_refunded' => $is_refunded,
            ]);
    }

    private function userNotification($requestData)
    {
        $userID = $requestData['userID'];
            
        $domainName = $requestData['domainName'];

        if (!$userID || !$domainName) {
            // Optionally handle missing data
            return;
        }

        DB::client()->table('notifications')->insert([
            'user_id'      => $userID,
            'title'        => 'Domain Deletion Request Approved',
            'message'      => 'Your request to delete the domain "' . $domainName . '" has been approved. The domain will be removed from your account and queued for deletion shortly. This action is final and cannot be undone.',
            'redirect_url' => '/domain',
            'created_at'   => now(),
            'importance'   => 'important',
        ]);
    }


    private function userEmailNotification($requestData)
    {

        $domainName = $requestData['domainName'];
        $userEmail = $requestData['userEmail'];

        $message = [
            'subject'  => 'Domain Deletion Request Approved',
            'greeting' => 'Greetings!',
            'body'     => 'Your request to delete the domain "' . $domainName . '" has been approved. The domain will be removed from your account and queued for deletion shortly. This action is final and cannot be undone.',
            'text'     => Carbon::now()->format('Y-m-d H:i:s'),
            'sender'   => 'StrangeDomains Support',
        ];

        Mail::to($userEmail)->send(new UserDeleteRequestMail($message));
    }

    private function newDomainDelete($data)
    {

        $date = Carbon::parse($data['createdDate']);

        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        DB::client()->table('domain_cancellation_requests')->insert([
            'user_id'      => $data['userID'],
            'domain_id'        => $data['domainId'],
            'reason'      => $data['reason'],
            'support_agent_id' => Auth::id(),
            'support_agent_name' =>  Auth::user()->name . ' (' . Auth::user()->email . ')',
            'deleted_at' => now(),
            'feedback_date' => now(),
            'support_note' => Auth::user()->name . ' (' . Auth::user()->email . ')' . " deleted domain:" . $data['domainName'],
            'is_refunded' => $is_refunded,
        ]);
    }

    private function domainHistory($data)
    {
        $userID = $data['userID'];
        $domainName = $data['domainName'];
        $domainId = $data['domainId'];

        event(new DomainHistoryEvent([
            'domain_id' => $domainId,
            'type'      => 'DOMAIN_DELETED',
            'user_id'   => $userID,
            'status'    => 'success',
            'message'   => 'Domain  "' . $domainName . '" deleted by ' . Auth::user()->name . ' (' . Auth::user()->email . ')',
            'payload'   => $data,
        ]));
    }
}
